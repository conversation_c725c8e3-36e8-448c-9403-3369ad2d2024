import UIKit
import OUICore
import OUICoreView
import ProgressHUD
import RxSwift
import AVFoundation  // 用于相机权限
import Photos       // 用于相册权限
import CoreLocation  // 用于定位权限
import Localize_Swift // 导入本地化库

// 定义UI样式键
let kChatStyleClassic = "style_classic"
let kChatStyleChillAsync = "style_chill_async"
let kSettingsTitle = "settings_title"
let kCancel = "cancel"

// 发布Chill功能的本地化键
let kPublishChill = "publish_chill"
let kChillMomentPlaceholder = "chill_moment_placeholder"
let kEnableLocationTracking = "enable_location_tracking"
let kDisableLocationTracking = "disable_location_tracking"
let kEnterContent = "please_enter_content"
let kPublishSuccess = "publish_success"
let kPublishFailure = "publish_failure"
let kMaxImagesError = "max_images_error"

open class ChatListViewController: UIViewController, UITableViewDelegate {
    // 添加UI风格属性和用户默认设置键
    private static let uiStyleKey = "ChatListUIStyleKey"
    
    private var currentStyle: ChatCellStyle = .classic {
        didSet {
            // 保存当前样式到UserDefaults
            UserDefaults.standard.set(currentStyle == .classic ? 0 : 1, forKey: ChatListViewController.uiStyleKey)
            // 刷新表格以应用新样式
            _tableView.reloadData()
        }
    }
    
    private lazy var _headerView: ChatListHeaderView = {
        let v = ChatListHeaderView()

        return v
    }()

    // 独立的状态条视图
    private lazy var _statusView: UIView = {
        let container = UIView()
        container.layer.masksToBounds = false
        container.layer.cornerRadius = StandardUI.cornerRadius
        container.backgroundColor = .c0089FF.withAlphaComponent(0.08)
        container.isHidden = true // 默认隐藏

        // 字母C指示器
        let letterLabel = UILabel()
        letterLabel.text = "C"
        letterLabel.textColor = UIColor.black // 黑色字母C
        letterLabel.font = UIFont(name: "Avenir-Heavy", size: 16) ?? UIFont.boldSystemFont(ofSize: 16)
        letterLabel.textAlignment = .center
        letterLabel.backgroundColor = UIColor.clear
        letterLabel.alpha = 1.0

        // 状态文字
        let statusLabel = UILabel()
        statusLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        statusLabel.text = "Loading..."
        statusLabel.textColor = UIColor.systemBlue
        statusLabel.numberOfLines = 1
        statusLabel.adjustsFontSizeToFitWidth = true
        statusLabel.minimumScaleFactor = 0.8
        statusLabel.backgroundColor = UIColor.clear

        container.addSubview(letterLabel)
        container.addSubview(statusLabel)

        letterLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        statusLabel.snp.makeConstraints { make in
            make.leading.equalTo(letterLabel.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualToSuperview().offset(-12)
        }

        // 不在这里设置高度，在外部约束中动态控制

        // 保存引用以便后续使用
        letterLabel.tag = 999 // letterLabel
        statusLabel.tag = 888 // statusLabel

        print("📱 StatusView created with letterLabel: \(letterLabel), statusLabel: \(statusLabel)")

        return container
    }()

    private lazy var _tableView: UITableView = {
        let v = UITableView()
        v.register(ChatTableViewCell.self, forCellReuseIdentifier: ChatTableViewCell.className)
        v.delegate = self
        v.separatorStyle = .none
        v.rowHeight = 68
        let refresh: DotRefreshControl = {
            let v = DotRefreshControl()
            v.rx.controlEvent(.valueChanged).subscribe(onNext: { [weak self, weak v] in
                // 开始刷新，不立即结束
                self?.refreshConversationsWithCompletion {
                    // 网络请求完成后才结束刷新
                    v?.endRefreshing()
                }
            }).disposed(by: _disposeBag)
            return v
        }()
        v.refreshControl = refresh
        v.backgroundColor = .white // 将背景色设置为白色
        
        return v
    }()

    override open func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }

    override open func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        navigationController?.setNavigationBarHidden(false, animated: true)
    }

    private lazy var _menuView: ChatMenuView = {
        let v = ChatMenuView()
        let scanItem = ChatMenuView.MenuItem(title: "扫一扫".innerLocalized(), icon: UIImage(nameInBundle: "chat_menu_scan_icon")) { [weak self] in
            let vc = ScanViewController()
            vc.scanDidComplete = { [weak self] (result: String) in
                if result.contains(IMController.addFriendPrefix) {
                    let uid = result.replacingOccurrences(of: IMController.addFriendPrefix, with: "")
                    let vc = UserDetailTableViewController(userId: uid, groupId: nil)
                    vc.hidesBottomBarWhenPushed = true
                    self?.navigationController?.pushViewController(vc, animated: true)
                    self?.dismiss(animated: false)
                } else if result.contains(IMController.joinGroupPrefix) {
                    let groupID = result.replacingOccurrences(of: IMController.joinGroupPrefix, with: "")
                    let vc = GroupDetailViewController(groupId: groupID)
                    vc.hidesBottomBarWhenPushed = true
                    self?.navigationController?.pushViewController(vc, animated: true)
                    self?.dismiss(animated: false)
                } else {
                    ProgressHUD.error(result)
                }
            }
            vc.modalPresentationStyle = .fullScreen
            self?.present(vc, animated: true, completion: nil)
        }
        let addFriendItem = ChatMenuView.MenuItem(title: "添加好友".innerLocalized(), icon: UIImage(nameInBundle: "chat_menu_add_friend_icon")) { [weak self] in
            let vc = SearchFriendViewController()
            vc.hidesBottomBarWhenPushed = true
            self?.navigationController?.pushViewController(vc, animated: true)
            vc.didSelectedItem = { [weak self] id in
                let vc = UserDetailTableViewController(userId: id, groupId: nil)
                self?.navigationController?.pushViewController(vc, animated: true)
            }
        }

        let addGroupItem = ChatMenuView.MenuItem(title: "添加群聊".innerLocalized(), icon: UIImage(nameInBundle: "chat_menu_add_group_icon")) { [weak self] in
            let vc = SearchGroupViewController()
            vc.hidesBottomBarWhenPushed = true
            self?.navigationController?.pushViewController(vc, animated: true)
            
            vc.didSelectedItem = { [weak self] id in
                let vc = GroupDetailViewController(groupId: id)
                self?.navigationController?.pushViewController(vc, animated: true)
            }
        }
        
        let createWorkGroupItem = ChatMenuView.MenuItem(title: "创建大群".innerLocalized(), icon: UIImage(nameInBundle: "chat_menu_create_work_group_icon")) { [weak self] in

            let vc = SelectContactsViewController()
            vc.selectedContact(blocked: [IMController.shared.uid]) { [weak self] (r: [ContactInfo]) in
                guard let sself = self else { return }
                let users = r.map {UserInfo(userID: $0.ID!, nickname: $0.name, faceURL: $0.faceURL)}
                let vc = NewGroupViewController(users: users, groupType: .working)
                self?.navigationController?.pushViewController(vc, animated: true)
            }
            vc.hidesBottomBarWhenPushed = true
            self?.navigationController?.pushViewController(vc, animated: true)
        }
        var items = [scanItem, addFriendItem, addGroupItem, createWorkGroupItem]

        v.setItems(items)
        
        return v
    }()

    public func refreshConversations() {
        _viewModel.getAllConversations()
    }

    // 带完成回调的刷新方法，用于下拉刷新
    private func refreshConversationsWithCompletion(_ completion: @escaping () -> Void) {
        let group = DispatchGroup()

        // 添加获取会话列表任务
        group.enter()
        _viewModel.getAllConversations()

        // 添加获取用户信息任务
        group.enter()
        _viewModel.getSelfInfo()

        // 模拟网络请求完成 - 在实际项目中，这些方法应该支持回调
        // 这里我们给一个合理的延迟时间，让用户看到加载效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            group.leave() // 会话列表完成
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
            group.leave() // 用户信息完成
        }

        // 所有任务完成后调用回调
        group.notify(queue: .main) {
            completion()
        }
    }
    
    private let _disposeBag = DisposeBag()
    private let _viewModel = ChatListViewModel()

    override open func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white // 将视图控制器的背景色也设置为白色
        
        // 加载保存的UI风格
        loadSavedUIStyle()
        
        initView()
        bindData()
        
        // 添加长按手势识别器到TabBar
        setupLongPressGestureOnTabBar()
        
        // 添加登录成功的通知监听
        NotificationCenter.default.addObserver(self, 
            selector: #selector(handleLoginSuccess), 
            name: NSNotification.Name("EventLoginSucceed"), 
            object: nil)
            
        // 初始化时主动刷新一次
        _viewModel.getAllConversations()
        _viewModel.getSelfInfo()
    }
    
    override open func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保当前样式被正确应用
        _tableView.reloadData()
        
        // 自动刷新获取最新消息
        _viewModel.getAllConversations()
        _viewModel.getSelfInfo()
    }
    
    // 加载保存的UI风格
    private func loadSavedUIStyle() {
        // 加载UI风格
        let savedStyle = UserDefaults.standard.integer(forKey: ChatListViewController.uiStyleKey)
        currentStyle = savedStyle == 0 ? .classic : .chillAsync
    }
    
    // 设置TabBar长按手势
    private func setupLongPressGestureOnTabBar() {
        guard let tabBarController = self.tabBarController else { return }
        
        // 创建长按手势识别器
        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(handleTabBarLongPress(_:)))
        longPressGesture.minimumPressDuration = 0.5 // 设置长按时间为0.5秒
        
        // 添加手势识别器到TabBar
        tabBarController.tabBar.addGestureRecognizer(longPressGesture)
    }
    
    // 处理TabBar长按事件
    @objc private func handleTabBarLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            // 获取长按位置
            let location = gesture.location(in: self.tabBarController?.tabBar)
            
            // 检查是否是在消息标签项上长按
            if let tabBar = self.tabBarController?.tabBar,
               let tabBarItems = tabBar.items,
               let messageTabIndex = tabBarItems.firstIndex(where: { $0.title == "消息".localized() }) {
                
                let tabBarWidth = tabBar.bounds.width
                let itemWidth = tabBarWidth / CGFloat(tabBarItems.count)
                let itemFrame = CGRect(x: itemWidth * CGFloat(messageTabIndex), y: 0, width: itemWidth, height: tabBar.bounds.height)
                
                if itemFrame.contains(location) {
                    // 在消息标签上长按，切换UI风格
                    toggleUIStyle()
                }
            }
        }
    }
    
    // 切换UI风格
    private func toggleUIStyle() {
        // 创建提示框
        let alert = UIAlertController(title: kSettingsTitle.innerLocalized(),
                                     message: nil,
                                     preferredStyle: .actionSheet)
        
        // 主色调 - 使用iOS默认蓝色
        let blueColor = UIColor(red: 0/255, green: 122/255, blue: 255/255, alpha: 1.0)
        
        // 自定义弹窗样式
        if let titleLabel = alert.view.subviews.first?.subviews.first?.subviews.first as? UILabel {
            titleLabel.font = UIFont(name: "Avenir", size: 15) ?? UIFont.systemFont(ofSize: 15)
            titleLabel.textColor = .black
            titleLabel.textAlignment = .center
        }
        
        // 自定义背景色和圆角
        if let alertView = alert.view {
            alertView.tintColor = blueColor
            alertView.layer.cornerRadius = 14
            alertView.clipsToBounds = true
        }
        
        // 添加经典风格选项
        let classicAction = UIAlertAction(title: kChatStyleClassic.innerLocalized(), style: .default) { [weak self] _ in
            self?.currentStyle = .classic
        }
        alert.addAction(classicAction)
        
        // 添加Chill非对称风格选项
        let chillAction = UIAlertAction(title: kChatStyleChillAsync.innerLocalized(), style: .default) { [weak self] _ in
            self?.currentStyle = .chillAsync
        }
        alert.addAction(chillAction)
        
        // 添加取消选项
        let cancelAction = UIAlertAction(title: kCancel.innerLocalized(), style: .cancel)
        alert.addAction(cancelAction)
        
        // 在iPad上的特殊处理
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = self.view
            popoverController.sourceRect = CGRect(x: self.view.bounds.midX, y: self.view.bounds.midY, width: 0, height: 0)
            popoverController.permittedArrowDirections = []
        }
        
        // 显示提示框
        present(alert, animated: true) {
            // 对action按钮应用自定义小号字体
            if let alertWindow = UIApplication.shared.windows.last,
               let alertController = alertWindow.rootViewController?.presentedViewController as? UIAlertController,
               let alertView = alertController.view.subviews.first,
               let alertContentView = alertView.subviews.first {
                
                let actionViews = alertContentView.subviews.filter { $0 is UIControl }
                
                for actionView in actionViews {
                    guard let button = actionView as? UIButton else { continue }
                    
                    if let title = button.title(for: .normal) {
                        // 根据按钮类型设置不同字体和样式 - 调整为更小的字体
                        button.titleLabel?.font = UIFont(name: "Avenir", size: 15) ?? UIFont.systemFont(ofSize: 15)
                        
                        // 为已选中的样式添加特殊标记
                        if title == kChatStyleClassic.innerLocalized() && self.currentStyle == .classic {
                            let checkmarkAttachment = NSTextAttachment()
                            let configuration = UIImage.SymbolConfiguration(pointSize: 12, weight: .regular)
                            checkmarkAttachment.image = UIImage(systemName: "checkmark", withConfiguration: configuration)?.withTintColor(blueColor)
                            checkmarkAttachment.bounds = CGRect(x: 0, y: -2, width: 12, height: 12)
                            
                            let attributedTitle = NSMutableAttributedString(string: "\(title) ")
                            attributedTitle.append(NSAttributedString(attachment: checkmarkAttachment))
                            button.setAttributedTitle(attributedTitle, for: .normal)
                        } 
                        else if title == kChatStyleChillAsync.innerLocalized() && self.currentStyle == .chillAsync {
                            let checkmarkAttachment = NSTextAttachment()
                            let configuration = UIImage.SymbolConfiguration(pointSize: 12, weight: .regular)
                            checkmarkAttachment.image = UIImage(systemName: "checkmark", withConfiguration: configuration)?.withTintColor(blueColor)
                            checkmarkAttachment.bounds = CGRect(x: 0, y: -2, width: 12, height: 12)
                            
                            let attributedTitle = NSMutableAttributedString(string: "\(title) ")
                            attributedTitle.append(NSAttributedString(attachment: checkmarkAttachment))
                            button.setAttributedTitle(attributedTitle, for: .normal)
                        }
                        else if title == kCancel.innerLocalized() {
                            button.setTitleColor(UIColor.darkGray, for: .normal)
                        }
                    }
                    
                    // 添加按钮的触感反馈
                    button.addTarget(self, action: #selector(self.buttonTapFeedback(_:)), for: .touchUpInside)
                }
                
                // 添加自定义分隔线
                for subview in alertContentView.subviews {
                    if let separator = subview as? UIView, separator.bounds.height <= 1 {
                        separator.backgroundColor = UIColor.lightGray.withAlphaComponent(0.2)
                    }
                }
            }
        }
    }

    // 添加触感反馈方法
    @objc private func buttonTapFeedback(_ sender: UIButton) {
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.prepare()
        generator.impactOccurred()
    }

    private func initView() {
        view.addSubview(_headerView)
        _headerView.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
        }

        // 添加状态条视图
        view.addSubview(_statusView)
        _statusView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.equalTo(_headerView.snp.bottom).offset(8)
            make.height.equalTo(0) // 初始高度为0
        }

        view.addSubview(_tableView)
        _tableView.snp.makeConstraints { make in
            make.top.equalTo(_statusView.snp.bottom).offset(8) // 连接到状态条下方
            make.leading.bottom.trailing.equalToSuperview()
        }
    }

    private func bindData() {
        
        IMController.shared.connectionRelay.subscribe(onNext: { [weak self] status in
            self?.updateConnectionStatus(status: status)
        }).disposed(by: _disposeBag)
        
        _headerView.addBtn.rx.tap.subscribe(onNext: { [weak self] in
            guard let self = self else { return }
            let publishVC = PublishArticleViewController()
            publishVC.shouldAutoEnableLocation = true
            let navController = UINavigationController(rootViewController: publishVC)
            navController.modalPresentationStyle = .fullScreen
            navController.interactivePopGestureRecognizer?.isEnabled = true
            navController.interactivePopGestureRecognizer?.delegate = publishVC
            self.present(navController, animated: true)
        }).disposed(by: _disposeBag)

        _viewModel.conversationsRelay.asDriver(onErrorJustReturn: []).drive(_tableView.rx.items) { [weak self] (tableView, row, item: ConversationInfo) in
            guard let self = self else {
                let cell = tableView.dequeueReusableCell(withIdentifier: ChatTableViewCell.className) as! ChatTableViewCell
                return cell
            }
            
            let cell = tableView.dequeueReusableCell(withIdentifier: ChatTableViewCell.className) as! ChatTableViewCell
            
            // 设置单元格的基本属性
            let placeholderName: String = item.conversationType == .c2c ? "contact_my_friend_icon" : "contact_my_group_icon"
            cell.avatarImageView.setAvatar(url: item.faceURL, text: item.showName, placeHolder: placeholderName)
            
            cell.titleLabel.text = item.showName
            
            // 获取消息摘要
            let messageAbstract = MessageHelper.getAbstructOf(conversation: item)
            
            // 处理消息截断，统一使用"..."样式
            let text = messageAbstract.string
            if text.count > 20 {
                // 创建已截断的富文本
                let truncatedText = NSMutableAttributedString(attributedString: 
                    messageAbstract.attributedSubstring(from: NSRange(location: 0, length: min(20, messageAbstract.length))))
                truncatedText.append(NSAttributedString(string: "..."))
                cell.subtitleLabel.attributedText = truncatedText
            } else {
                cell.subtitleLabel.attributedText = messageAbstract
            }
            
            cell.timeLabel.text = MessageHelper.convertList(timestamp_ms: item.latestMsgSendTime)
            
            // 设置未读消息和静音状态
            var unreadShouldHide: Bool = item.recvMsgOpt != .receive || item.unreadCount <= 0
            cell.unreadLabel.isHidden = unreadShouldHide
            cell.unreadLabel.text = "\(item.unreadCount)"
            cell.muteImageView.isHidden = item.recvMsgOpt == .receive
            
            // 确保UI样式一致
            cell.setCellStyle(self.currentStyle)
            
            // 根据UI风格调整行高
            if self.currentStyle == .chillAsync {
                tableView.rowHeight = 82 // Chill非对称风格的行高
            } else {
                tableView.rowHeight = 68 // 经典风格的行高
            }
            
            return cell
        }.disposed(by: _disposeBag)

        _tableView.rx.modelSelected(ConversationInfo.self).subscribe(onNext: { [weak self] (conversation: ConversationInfo) in
            
            let vc = ChatViewControllerBuilder().build(conversation)
            vc.hidesBottomBarWhenPushed = true
            self?.navigationController?.pushViewController(vc, animated: true)
        }).disposed(by: _disposeBag)

        // 立即设置"Chill"文案，不等登录完成
        _headerView.nameLabel.text = "Chill"
        _headerView.nameLabel.textColor = .black
        _headerView.nameLabel.font = UIFont(name: "Avenir", size: 17)

        // 临时测试状态条
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            print("🧪 Testing status view with connecting status")
            self?.updateConnectionStatus(status: .connecting)
        }

        _viewModel.loginUserPublish.subscribe(onNext: { [weak self] (userInfo: UserInfo?) in
            // 登录完成后可以做其他更新，但"Chill"文案已经显示了
            // 这里可以添加其他需要登录后才能显示的内容
        }).disposed(by: _disposeBag)
    }

    public func tableView(_ tableView: UITableView, willPerformPreviewActionForMenuWith configuration: UIContextMenuConfiguration, animator: UIContextMenuInteractionCommitAnimating) {
        DispatchQueue.main.async {
            if let actions = animator.previewViewController?.view.subviews.first?.subviews.first?.subviews {
                for case let action as UIButton in actions {
                    action.titleLabel?.font = UIFont(name: "Avenir", size: 17)
                    action.setTitleColor(.white, for: .normal)
                }
            }
        }
    }
    
    @objc private func handleLoginSuccess() {
        // 登录成功后主动刷新会话列表
        _viewModel.getAllConversations()
        _viewModel.getSelfInfo()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// 首先定义 PlaceholderTextView
class PlaceholderTextView: UITextView {
    private let placeholderLabel: UILabel = {
        let label = UILabel()
        label.textColor = .lightGray
        label.numberOfLines = 0
        return label
    }()
    
    var placeholder: String? {
        get { return placeholderLabel.text }
        set {
            placeholderLabel.text = newValue
            placeholderLabel.sizeToFit()
            placeholderLabel.frame.origin = CGPoint(x: 5, y: 8)
            updatePlaceholderVisibility()
        }
    }
    
    override var text: String! {
        didSet {
            updatePlaceholderVisibility()
        }
    }
    
    override init(frame: CGRect, textContainer: NSTextContainer?) {
        super.init(frame: frame, textContainer: textContainer)
        setupPlaceholder()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupPlaceholder()
    }
    
    private func setupPlaceholder() {
        addSubview(placeholderLabel)
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(textDidChange),
                                             name: UITextView.textDidChangeNotification,
                                             object: nil)
    }
    
    @objc private func textDidChange() {
        updatePlaceholderVisibility()
    }
    
    private func updatePlaceholderVisibility() {
        placeholderLabel.isHidden = !text.isEmpty
    }
}

// 然后定义 PublishArticleViewController
class PublishArticleViewController: UIViewController, CLLocationManagerDelegate {
    // MARK: - Properties
    private let locationManager = CLLocationManager()
    private var isLocationTrackingEnabled = false
    private let locationTrackingText = "开启持续定位"
    private let locationTrackingOffText = "关闭持续定位"  // 添加关闭时的提示文本
    private var originalText: String = ""
    private var selectedImages: [UIImage] = []
    private var currentLocation: CLLocationCoordinate2D?
    private let maxImageCount = 9
    
    private let lightGreenColor = UIColor(red: 0/255, green: 200/255, blue: 132/255, alpha: 1.0)
    
    private lazy var cancelButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle(kCancel.innerLocalized(), for: .normal)
        btn.setTitleColor(.black, for: .normal) // 设置黑色
        btn.titleLabel?.font = UIFont(name: "Avenir", size: 16)
        btn.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var publishButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle(kPublishChill.innerLocalized(), for: .normal)
        btn.backgroundColor = lightGreenColor // 设置浅
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = UIFont(name: "Avenir", size: 16)
        btn.layer.cornerRadius = 15
        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 15, bottom: 5, right: 15) // 添加内边距使按钮更大
        btn.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        return btn
    }()
    
    private lazy var toolBar: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44))
        view.backgroundColor = .white
        view.layer.borderWidth = 0.5
        view.layer.borderColor = UIColor.lightGray.withAlphaComponent(0.3).cgColor
        
        let stackView = UIStackView()
        stackView.distribution = .equalSpacing
        stackView.alignment = .center
        stackView.spacing = 25
        view.addSubview(stackView)
        
        stackView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(15)
            make.centerY.equalToSuperview()
        }
        
        let buttonConfigs = [
            (image: "inputbar_pad_camera_icon", tag: 0),
            (image: "inputbar_pad_album_icon", tag: 1),
            (image: "location_me_select", tag: 2)
        ]
        
        buttonConfigs.forEach { config in
            let button = UIButton(type: .custom)
            if let image = UIImage(nameInBundle: config.image) {
                button.setImage(image.withRenderingMode(.alwaysOriginal), for: .normal)
            }
            button.tag = config.tag
            button.addTarget(self, action: #selector(toolButtonTapped(_:)), for: .touchUpInside)
            button.snp.makeConstraints { make in
                make.size.equalTo(CGSize(width: 29, height: 29))
            }
            stackView.addArrangedSubview(button)
        }
        
        return view
    }()
    
    private lazy var textView: PlaceholderTextView = {
        let tv = PlaceholderTextView()
        tv.font = UIFont(name: "Avenir", size: 16)
        tv.text = ""
        tv.placeholder = kChillMomentPlaceholder.innerLocalized()
        tv.inputAccessoryView = toolBar  // 设置输入附件视图
        return tv
    }()
    
    private let imageItemSize: CGFloat = 100  // 调整图片大小
    private let imageSpacing: CGFloat = 8     // 减小间距
    private let maxImagesPerRow = 3           // 每行最多显示3张图片
    
    // MARK: - UI Components
    private lazy var imageCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical      // 改为垂直滚动
        layout.itemSize = CGSize(width: imageItemSize, height: imageItemSize)
        layout.minimumInteritemSpacing = imageSpacing
        layout.minimumLineSpacing = imageSpacing
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.register(ImagePreviewCell.self, forCellWithReuseIdentifier: "ImagePreviewCell")
        cv.delegate = self
        cv.dataSource = self
        cv.showsVerticalScrollIndicator = false
        cv.isScrollEnabled = false             // 禁用滚动
        return cv
    }()
    
    var shouldAutoEnableLocation: Bool = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        setupImageCollectionView()
        
        // 初始化时就请求位置权限并获取位置
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters // 使用较低精度以节省电量
        locationManager.requestWhenInUseAuthorization()
        
        // 如果已经有权限，直接获取位置
        if CLLocationManager.authorizationStatus() == .authorizedWhenInUse {
            locationManager.requestLocation() // 只获取一次位置
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        textView.becomeFirstResponder()
    }
    
    private func setupNavigationBar() {
        // 设置导航栏样式
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        
        // 启滑动返回
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
        navigationController?.interactivePopGestureRecognizer?.delegate = self
    }
    
    private func setupUI() {
        view.backgroundColor = .white
        
        // Add navigation items
        navigationItem.leftBarButtonItem = UIBarButtonItem(customView: cancelButton)
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: publishButton)
        
        // Add textView
        view.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(10)
            make.left.right.equalToSuperview().inset(15)
            make.height.equalTo(200)
        }
    }
    
    @objc private func toolButtonTapped(_ sender: UIButton) {
        switch sender.tag {
        case 0: // 相机
            checkCameraPermission()
        case 1: // 相册
            checkPhotoLibraryPermission()
        case 2: // 定位
            toggleLocationTracking(sender)
        default:
            break
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc private func cancelButtonTapped() {
        textView.resignFirstResponder()
        dismiss(animated: true)
    }
    
    @objc private func publishButtonTapped() {
        guard !textView.text.isEmpty else {
            showCustomMessage(kEnterContent.innerLocalized())
            return
        }
        
        // 如果没有位置信息，尝试再次获取
        if currentLocation == nil {
            locationManager.requestLocation()
        }
        
        // 准备发布数据
        let content = textView.text!
        let location_x = currentLocation?.longitude ?? 0
        let location_y = currentLocation?.latitude ?? 0
        
        print("📍 Publishing with location: \(location_x), \(location_y)")
        
        // 调用API发布
        WebAPI.shared.publishArticle(
            content: content,
            location_x: location_x,
            location_y: location_y,
            images: selectedImages
        ) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self?.showCustomMessage(kPublishSuccess.innerLocalized())
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        self?.dismiss(animated: true)
                    }
                case .failure(let error):
                    self?.showCustomMessage("\(kPublishFailure.innerLocalized()): \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - Image Handling
    private func handleSelectedImage(_ image: UIImage) {
        guard selectedImages.count < maxImageCount else {
            ProgressHUD.error(kMaxImagesError.innerLocalized())
            return
        }
        
        // 优化图片压缩设置
        let processedImage = processImage(image)
        selectedImages.append(processedImage)
        imageCollectionView.reloadData()
    }

    private func processImage(_ image: UIImage) -> UIImage {
        // 1. 保持较大的尺寸
        let maxSize: CGFloat = 1600  // 增加最大尺寸到 1600
        
        // 2. 计算新的尺寸，保持宽高比
        let size = image.size
        let scale = max(size.width, size.height) / maxSize
        
        if scale <= 1 {
            // 如果图片本身于最大尺寸，使用较高的压缩质量
            if let data = image.jpegData(compressionQuality: 0.9),
               let compressedImage = UIImage(data: data) {
                return compressedImage
            }
            return image
        }
        
        // 3. 等比例缩放
        let newSize = CGSize(
            width: size.width / scale,
            height: size.height / scale
        )
        
        // 4. 使用高质量渲染选项
        let renderer = UIGraphicsImageRenderer(size: newSize)
        let newImage = renderer.image { context in
            // 设置高质量渲染
            context.cgContext.interpolationQuality = .high
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }
        
        // 5. 使用较高的压缩质量
        if let data = newImage.jpegData(compressionQuality: 0.8),
           let compressedImage = UIImage(data: data) {
            return compressedImage
        }
        
        return newImage
    }
    
    // MARK: - CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        if let location = locations.last {
            currentLocation = location.coordinate
            print("📍 Got location: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ Location error:", error)
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        if status == .authorizedWhenInUse {
            locationManager.requestLocation()
        }
    }
    
    // MARK: - Permission Methods
    private func checkCameraPermission() {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            openCamera()
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.openCamera()
                    } else {
                        ProgressHUD.error("需要相机权限才能使用此功能")
                    }
                }
            }
        case .denied, .restricted:
            showPermissionAlert(for: "相机")
        @unknown default:
            break
        }
    }
    
    private func checkPhotoLibraryPermission() {
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .authorized:
            openPhotoLibrary()
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    if status == .authorized {
                        self?.openPhotoLibrary()
                    } else {
                        ProgressHUD.error("需要相册权限才能使用此功能")
                    }
                }
            }
        case .denied, .restricted:
            showPermissionAlert(for: "相册")
        @unknown default:
            break
        }
    }
    
    // 修改 toggleLocationTracking 方法，只在手动切换时显示提示
    private func toggleLocationTracking(_ sender: UIButton) {
        if !isLocationTrackingEnabled {
            // 开启位置追踪
            isLocationTrackingEnabled = true
            locationManager.startUpdatingLocation()
            // UI反馈
            sender.setImage(UIImage(nameInBundle: "loction_me_icon"), for: .normal)
            showCustomMessage(kEnableLocationTracking.innerLocalized())  // 只在手动开启时显示提示
        } else {
            // 关闭位置追踪
            isLocationTrackingEnabled = false
            locationManager.stopUpdatingLocation()
            // UI反馈
            sender.setImage(UIImage(nameInBundle: "location_me_select"), for: .normal)
            showCustomMessage(kDisableLocationTracking.innerLocalized())
        }
    }
    
    // 定义消息显示
    private func showCustomMessage(_ message: String) {
        let messageView = UIView()
        messageView.backgroundColor = lightGreenColor
        messageView.alpha = 0
        messageView.layer.cornerRadius = 16  // 添加圆角
        
        let label = UILabel()
        label.text = message
        label.textColor = .white
        label.font = UIFont(name: "Avenir", size: 17)
        label.textAlignment = .center
        
        view.addSubview(messageView)
        messageView.addSubview(label)
        
        messageView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(10)
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalTo(32)
        }
        
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16))
        }
        
        UIView.animate(withDuration: 0.3) {
            messageView.alpha = 1
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            UIView.animate(withDuration: 0.3, animations: {
                messageView.alpha = 0
            }) { _ in
                messageView.removeFromSuperview()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func showPermissionAlert(for type: String) {
        let alert = UIAlertController(
            title: "需要\(type)权限",
            message: "请在设置中开启\(type)权限",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        })
        
        present(alert, animated: true)
    }
    
    private func openCamera() {
        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            let picker = UIImagePickerController()
            picker.sourceType = .camera
            picker.delegate = self
            present(picker, animated: true)
        }
    }
    
    private func openPhotoLibrary() {
        if UIImagePickerController.isSourceTypeAvailable(.photoLibrary) {
            let picker = UIImagePickerController()
            picker.sourceType = .photoLibrary
            picker.delegate = self
            present(picker, animated: true)
        }
    }
    
    private func stopLocationTracking() {
        isLocationTrackingEnabled = false
        locationManager.stopUpdatingLocation()
    }
    
    // MARK: - Progress HUD
    private func showLoading(_ message: String) {
        ProgressHUD.animate(message, interaction: false)
    }
    
    private func showSuccess(_ message: String) {
        ProgressHUD.success(message)
    }
    
    private func showError(_ message: String) {
        ProgressHUD.error(message)
    }
    
    // MARK: - Setup Methods
    private func setupImageCollectionView() {
        view.addSubview(imageCollectionView)
        
        // 计算CollectionView的高度
        let rows = ceil(CGFloat(maxImageCount) / CGFloat(maxImagesPerRow))
        let totalHeight = rows * imageItemSize + (rows - 1) * imageSpacing
        
        imageCollectionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)  // 设置左右边距
            make.top.equalTo(textView.snp.bottom).offset(12)
            make.height.equalTo(totalHeight)
        }
    }
    
    // MARK: - Button Actions
    @objc private func deleteButtonTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < selectedImages.count else { return }
        
        // 移除图片
        selectedImages.remove(at: index)
        
        // 刷新集视图
        imageCollectionView.reloadData()
    }
}

// MARK: - UIImagePickerControllerDelegate
extension PublishArticleViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    public func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)
        
        if let image = info[.originalImage] as? UIImage {
            handleSelectedImage(image)
        }
    }
    
    public func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// 修改手势代理实现
extension PublishArticleViewController: UIGestureRecognizerDelegate {
    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        if let nav = navigationController {
            return nav.viewControllers.count > 1
        }
        return true
    }
    
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

// WebAPI 实现
class WebAPI {
    static let shared = WebAPI()
    
    func sendLocation(_ locationData: [String: Any], completion: @escaping (Result<Void, Error>) -> Void) {
        // 这里实现实际的 API 调用
        // 示例：
        guard let url = URL(string: "YOUR_API_ENDPOINT") else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: locationData)
            request.httpBody = jsonData
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }
                completion(.success(()))
            }.resume()
        } catch {
            completion(.failure(error))
        }
    }
    
    func publishArticle(
        content: String,
        location_x: Double,
        location_y: Double,
        images: [UIImage],
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        guard let url = URL(string: "\(GlobalConfig.apiBaseURL)/publish") else {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        // 创建 URLRequest
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 获取当前用户ID
        let userID = IMController.shared.uid
        
        // 准备请求数据
        var requestData: [String: Any] = [
            "content": content,
            "location_x": location_x,
            "location_y": location_y,
            "user_id": userID,
            "images": []
        ]
        
        // 处理图片
        if !images.isEmpty {
            let processedImages = images.compactMap { image -> String? in
                guard let imageData = image.jpegData(compressionQuality: 0.8) else { return nil }
                return imageData.base64EncodedString()
            }
            requestData["images"] = processedImages
        }
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestData)
            request.httpBody = jsonData
            
            print("开始发送请求...")
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("网络请求错误: \(error.localizedDescription)")
                        // 即使有错误，也检查是否有响应
                        if let httpResponse = response as? HTTPURLResponse {
                            print("服务器返回状态码：\(httpResponse.statusCode)")
                            if httpResponse.statusCode == 200 {
                                // 如果服务器返回了 200，就认为是成功的
                                print("服务器返回成功状态码，忽略网络错误")
                                completion(.success(()))
                                return
                            }
                        }
                        completion(.failure(error))
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse {
                        print("服务器返回状态码：\(httpResponse.statusCode)")
                        if httpResponse.statusCode == 200 {
                            completion(.success(()))
                        } else {
                            let error = NSError(domain: "", code: httpResponse.statusCode, 
                                userInfo: [NSLocalizedDescriptionKey: "服务器返回错误状态码：\(httpResponse.statusCode)"])
                            completion(.failure(error))
                        }
                    } else {
                        completion(.failure(NSError(domain: "", code: -1, 
                            userInfo: [NSLocalizedDescriptionKey: "无效的响应"])))
                    }
                }
            }
            task.resume()
            
        } catch {
            print("JSON序列化错误: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
}

// MARK: - UICollectionViewDataSource
extension PublishArticleViewController: UICollectionViewDataSource {
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        // 只回已选择的图片数量，不再添加额外的 cell 用于显示 "+" 按钮
        return selectedImages.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ImagePreviewCell", for: indexPath) as! ImagePreviewCell
        
        // 显示选择图片
        cell.configure(with: selectedImages[indexPath.item])
        cell.deleteButton.isHidden = false
        cell.deleteButton.tag = indexPath.item
        cell.deleteButton.addTarget(self, action: #selector(deleteButtonTapped(_:)), for: .touchUpInside)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PublishArticleViewController: UICollectionViewDelegate {
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 移除点击 cell 的处理，因为不再需要处理 "+" 按钮的点击事件
    }
}

// MARK: - ImagePreviewCell
class ImagePreviewCell: UICollectionViewCell {
    let imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.backgroundColor = UIColor(red: 245/255, green: 245/255, blue: 245/255, alpha: 1.0)
        return iv
    }()
    
    let deleteButton: UIButton = {
        let button = UIButton(type: .custom)
        let buttonSize: CGFloat = 22
        button.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        button.layer.cornerRadius = buttonSize / 2
        
        let symbolConfig = UIImage.SymbolConfiguration(pointSize: 12, weight: .medium)
        let image = UIImage(systemName: "xmark", withConfiguration: symbolConfig)
        button.setImage(image, for: .normal)
        button.tintColor = .white
        
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        contentView.addSubview(imageView)
        contentView.addSubview(deleteButton)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        deleteButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.right.equalToSuperview().offset(-4)
            make.size.equalTo(CGSize(width: 22, height: 22))
        }
    }
    
    func configure(with image: UIImage) {
        imageView.image = image
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
    }
}

// MARK: - ChatListViewController Extension
extension ChatListViewController {
    // 更新连接状态
    private func updateConnectionStatus(status: ConnectionStatus) {
        // 只在连接失败或同步失败时显示，连接成功和同步完成时隐藏
        let shouldShow = (status == .connectFailure || status == .syncFailure || status == .connecting || status == .syncStart)
        print("🔄 updateConnectionStatus called, status: \(status), shouldShow: \(shouldShow)")

        // 获取状态文字标签
        if let statusLabel = _statusView.viewWithTag(888) as? UILabel {
            statusLabel.text = status.title

            switch status {
            case .connectFailure, .syncFailure:
                statusLabel.textColor = UIColor.systemRed
                _statusView.backgroundColor = UIColor.systemRed.withAlphaComponent(0.08)
            case .connecting, .syncStart:
                statusLabel.textColor = UIColor.systemBlue
                _statusView.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.08)
            case .connected, .syncComplete:
                // 连接成功和同步完成时不显示
                break
            case .kickedOffline:
                break
            }
        }

        // 控制字母C的动画
        if let letterLabel = _statusView.viewWithTag(999) as? UILabel {
            if shouldShow {
                // 启动闪烁动画
                let pulseAnimation = CAKeyframeAnimation(keyPath: "opacity")
                pulseAnimation.values = [1.0, 0.5, 1.0]
                pulseAnimation.keyTimes = [0.0, 0.5, 1.0]
                pulseAnimation.duration = 0.8
                pulseAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                pulseAnimation.repeatCount = .infinity
                letterLabel.layer.add(pulseAnimation, forKey: "letterPulse")
            } else {
                letterLabel.layer.removeAnimation(forKey: "letterPulse")
                letterLabel.alpha = 1.0
            }
        }

        // 显示/隐藏状态条，动态调整高度以释放空间
        let targetHeight = shouldShow ? 36 : 0
        print("📏 Updating height to: \(targetHeight)")
        print("📦 StatusView current frame: \(_statusView.frame)")
        print("🏗️ StatusView superview: \(_statusView.superview)")
        print("🔗 StatusView has constraints: \(_statusView.constraints.count)")

        // 先更新约束
        _statusView.snp.updateConstraints { make in
            make.height.equalTo(targetHeight)
        }

        print("📏 Constraint updated, forcing layout...")
        view.setNeedsLayout()
        view.layoutIfNeeded()
        print("📦 StatusView frame after constraint update: \(_statusView.frame)")

        // 然后执行动画
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut], animations: { [weak self] in
            self?._statusView.alpha = shouldShow ? 1.0 : 0.0
            self?.view.layoutIfNeeded() // 触发约束更新
        }, completion: { [weak self] _ in
            print("📦 StatusView final frame: \(self?._statusView.frame ?? .zero)")
            print("👁️ StatusView final alpha: \(self?._statusView.alpha ?? 0)")
        })
    }
}
